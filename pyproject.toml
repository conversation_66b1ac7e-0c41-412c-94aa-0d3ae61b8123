[project]
name = "deploy"
version = "0.1.0"
requires-python = ">=3.13"
dependencies = [
    "matplotlib>=3.10.3",
    "opencv-python>=4.12.0.88",
    "pandas>=2.3.1",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
    "seaborn>=0.13.2",
    "torch>=2.7.1",
    "torchvision>=0.22.1",
    "tqdm>=4.67.1",
    "zmq>=0.0.0",
]
[tool.uv.sources]
torch = [
  { index = "torch-gpu", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
torchvision = [
  { index = "torch-gpu", marker = "sys_platform == 'linux' or sys_platform == 'win32'" },
]
[[tool.uv.index]]
name = "torch-gpu"
url = "https://mirrors.nju.edu.cn/pytorch/whl/cu128"
explicit = true
