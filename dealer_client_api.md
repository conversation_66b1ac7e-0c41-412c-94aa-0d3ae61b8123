# 目标检测服务 API 文档

## 1. 概述

本 API 提供基于 ZeroMQ (ZMQ) 的图像目标检测服务，支持多种安全生产相关的场景检测。客户端通过 ZMQ `DEALER` 套接字发送请求，服务器进行实时分析并返回结果。

## 2. 连接信息

- **协议**: ZMQ (DEALER-ROUTER)
- **地址**: `tcp://127.0.0.1:5559` (可在客户端和服务器配置中修改)

## 3. 请求格式

所有请求均为 JSON 格式。

```json
{
    "interface": "API_ID",
    "api_key": "YOUR_API_KEY",
    "image_base64": ["BASE64_ENCODED_IMAGE"],
    "THRESHOLD_PARAM": 0.5
}
```

### 3.1. 请求参数

| 参数名 | 类型 | 是否必须 | 描述 |
| :--- | :--- | :--- | :--- |
| `interface` | string | 是 | 接口ID，用于指定要执行的检测算法。详见 [接口ID列表](#4-接口id列表)。 |
| `api_key` | string | 是 | API 密钥，用于认证 (当前版本中未使用，可传空字符串 `""`)。 |
| `image_base64` | array | 是 | 包含单张图像 Base64 编码字符串的数组。 |
| `THRESHOLD_PARAM` | float | 否 | 检测阈值 (0.0 - 1.0)。参数名根据 `interface` 的不同而变化，详见 [接口ID列表](#4-接口id列表)。若不提供，则使用服务器默认阈值。 |

## 4. 接口ID列表

| 接口ID (`interface`) | 检测功能 | 阈值参数 (`THRESHOLD_PARAM`) | 默认阈值 | 响应结果字段 |
| :--- | :--- | :--- | :--- | :--- |
| `38` | 安全帽佩戴检测 | `helmet_th` | 0.5 | `people_helmet_detector` |
| `30` | 烟火检测 | `smoke_threshold`, `fire_threshold` | 0.5, 0.5 | `smoke_fire_info` |
| `41` | 工装穿戴检测 | `clothes_th` | 0.5 | `people_jacket_detector` |
| `35` | 人员抽烟检测 | `people_smoke_threshold` | 0.5 | `people_smoke_detector` |
| `31` | 区域入侵检测 | `people_threshold` | 0.5 | `region_invade_info` |
| `50` | 安全带佩戴检测 | `detect_th` | 0.5 | `obj_detector` |

## 5. 响应格式

服务器返回 JSON 格式的响应。

### 5.1. 成功响应

```json
{
    "error_id": 0,
    "error_message": "success",
    "RESPONSE_FIELD": [
        {
            "info": [
                {
                    "rect": [x, y, width, height],
                    "label": "detection_label"
                }
            ]
        }
    ],
    "request_id": "a_unique_request_id",
    "time_used": 123
}
```

- `RESPONSE_FIELD`: 响应结果字段名，与请求的 `interface` 对应，详见 [接口ID列表](#4-接口id列表)。
- `info`: 检测到的目标信息数组。
- `rect`: 目标边界框，格式为 `[左上角x, 左上角y, 宽度, 高度]`。
- `label`: 检测结果的标签 (例如: `"no-helmet"`)。

### 5.2. 失败响应

```json
{
    "error_id": 10011,
    "error_message": "API ID错误"
}
```

### 5.3. 错误码

| 错误码 (`error_id`) | 描述 |
| :--- | :--- |
| `0` | 成功 |
| `10001` | API Key 错误 |
| `10005` | JSON 参数错误 |
| `10007` | 图像 Base64 编码错误 |
| `10011` | 接口ID (`interface`) 错误 |
| `10012` | 图像为空 |

## 6. 使用示例

### 6.1. 安全帽检测请求

```json
{
    "interface": "38",
    "api_key": "",
    "image_base64": ["/9j/4AAQSk..."],
    "helmet_th": 0.6
}
```

### 6.2. 烟火检测请求

```json
{
    "interface": "30",
    "api_key": "",
    "image_base64": ["/9j/4AAQSk..."],
    "smoke_threshold": 0.55,
    "fire_threshold": 0.65
}
```
