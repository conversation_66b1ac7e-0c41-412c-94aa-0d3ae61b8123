# -*- coding: utf-8 -*-
"""
Created on Sat May  7 13:26:29 2022

@author: Jerry
"""

personModel = 'weights/person.pt'
fireModel = 'weights/fire.pt'
poseModel = 'weights/cigar.pth'
safetybeltModel = 'weights/safetybelt.pt'
class CONFIG:
    def __init__(self):
        self.API_KEY = ''
        # API_ID
        self.API_ID_HELMET = '38'  # 安全帽检测
        self.API_ID_SMOKE_FIRE = '30'  # 烟火检测
        self.API_ID_UNIFORM = '41'  # 正确着装
        self.API_ID_SMOKE = '35'  # 抽烟检测
        self.API_ID_INVADE = '31'  # 入侵检测
        self.API_ID_SAFEBELT = '50'  # 安全带检测
        self.API_ID_LIST = [self.API_ID_HELMET, self.API_ID_SMOKE_FIRE, self.API_ID_UNIFORM,
                            self.API_ID_SMOKE, self.API_ID_INVADE, self.API_ID_SAFEBELT]
        # MODEL_PATH 
        self.HELMET_PATH = personModel  # 安全帽检测
        self.SMOKE_FIRE_PATH = fireModel  # 烟火检测
        self.UNIFORM_PATH = personModel  # 正确着装
        self.SMOKE_PATH = personModel  # 抽烟检测
        self.SMOKE_POSE_PATH = poseModel  # 抽烟姿态检测
        self.INVADE_PATH = personModel  # 入侵检测
        self.SAFEBELT_PATH = safetybeltModel  # 安全带检测

        # INTERFACE THRESHOLD PARAM
        self.PARAM_HELMET = "helmet_th"
        self.PARAM_SMOKE_FIRE_SMOKE = "smoke_threshold"
        self.PARAM_SMOKE_FIRE_FIRE = "fire_threshold"
        self.PARAM_UNIFORM = "clothes_th"
        self.PARAM_SMOKE = "people_smoke_threshold"
        self.PARAM_INVADE = "people_threshold"
        self.PARAM_SAFEBELT = "detect_th"  # 待定

        # INTERFACE THRESHOLD
        self.PARAM_HELMET_THRESHOLD = 0.5  # 0.6
        self.PARAM_SMOKE_FIRE_SMOKE_THRESHOLD = 0.5  # 0.7
        self.PARAM_SMOKE_FIRE_FIRE_THRESHOLD = 0.5  # 0.7
        self.PARAM_UNIFORM_THRESHOLD = 0.5  # 0.7
        self.PARAM_SMOKE_THRESHOLD = 0.5
        self.PARAM_INVADE_THRESHOLD = 0.5
        self.PARAM_SAFEBELT_THRESHOLD = 0.5  # 0.6

        self.SERVER_IP = 'tcp://*:5559'

        self.FILTER_IDS = {self.API_ID_HELMET: [0, 1, 2],
                           self.API_ID_SMOKE_FIRE: [0, 1],
                           self.API_ID_UNIFORM: [0, 1],
                           self.API_ID_SMOKE: [0, 1],
                           self.API_ID_INVADE: [0, 1],
                           self.API_ID_SAFEBELT: [0, 1, 2]  # 'offground', 'ground', 'safebelt'
                           }
        self.AREA_FILTER_PARAMS = {self.API_ID_HELMET: {'filter_id': [0, 1], 'filter_percent': 0.002},
                                   self.API_ID_SMOKE_FIRE: {'filter_id': [0, 1], 'filter_percent': 0.005},
                                   self.API_ID_UNIFORM: {'filter_id': [0, 1], 'filter_percent': 0.002},
                                   self.API_ID_INVADE: {'filter_id': [0, 1], 'filter_percent': 0.002},
                                   self.API_ID_SAFEBELT: {'filter_id': [0, 1], 'filter_percent': 0.005}
                                   # 'offground', 'ground', 'safebelt'
                                   }
        self.ERROR_MESSAGE = {self.API_ID_HELMET: "helmet detect success",
                              self.API_ID_SMOKE_FIRE: "smoke fire detect success",
                              self.API_ID_UNIFORM: "lifejacket detect success",
                              self.API_ID_SMOKE: "people smoke detect success",
                              self.API_ID_INVADE: "region invade detect success",
                              self.API_ID_SAFEBELT: "safebelt detect success"
                              }
        self.DETECTOR = {self.API_ID_HELMET: "people_helmet_detector",
                         self.API_ID_SMOKE_FIRE: "smoke_fire_info",
                         self.API_ID_UNIFORM: "people_jacket_detector",
                         self.API_ID_SMOKE: "people_smoke_detector",
                         self.API_ID_INVADE: "region_invade_info",
                         self.API_ID_SAFEBELT: "obj_detector"
                         }

        # INTERFACE PARAM THRESHOLD DICT
        self.INTERFACE_THRESHOLD_DICT = {
            self.API_ID_HELMET: [self.PARAM_HELMET, self.PARAM_HELMET_THRESHOLD],  # {'helmet_th':0.6},
            self.API_ID_SMOKE_FIRE: {self.PARAM_SMOKE_FIRE_SMOKE: self.PARAM_SMOKE_FIRE_SMOKE_THRESHOLD,
                                     self.PARAM_SMOKE_FIRE_FIRE: self.PARAM_SMOKE_FIRE_FIRE_THRESHOLD},
            self.API_ID_UNIFORM: [self.PARAM_UNIFORM, self.PARAM_UNIFORM_THRESHOLD],
            self.API_ID_SMOKE: [self.PARAM_SMOKE, self.PARAM_SMOKE_THRESHOLD],
            self.API_ID_INVADE: [self.PARAM_INVADE, self.PARAM_INVADE_THRESHOLD],
            self.API_ID_SAFEBELT: [self.PARAM_SAFEBELT, self.PARAM_SAFEBELT_THRESHOLD]
        }
