# -*- coding: utf-8 -*-
"""
Created on Thu May  5 11:01:12 2022

@author: Z20070346
"""
import glob
import os
import threading
import time
import uuid

import cv2
import numpy as np
import zmq
from PIL import Image, ImageDraw, ImageFont

from utils.api_config import CONFIG
from utils.tool import cv2_to_base64

# Set number of clients that will make simultaneous requests
NUMBER_OF_CLIENTS = 1

# Set how long it will take each request to be processed by the server
PROCESSING_TIME = 5

config = CONFIG()
save_img_dir = 'infer_results_BRG/'
os.makedirs(save_img_dir, exist_ok=True)

error_dict = {'38': '未佩戴安全帽',
              '30': '烟火报警',
              '41': '未穿戴工装',
              '35': '人员抽烟',
              '31': '人员入侵',
              '50': '未佩戴安全带'}


def split_list(ori_list, n):
    if len(ori_list) % n == 0:
        cnt = len(ori_list) // n
    else:
        cnt = len(ori_list) // n + 1

    for i in range(0, n):
        yield ori_list[i * cnt:(i + 1) * cnt]


def cv2ImgAddText(img, text, left, top, textColor=(255, 0, 0), textSize=20):
    img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img)
    fontText = ImageFont.truetype("font/simsun.ttc", textSize, encoding="utf-8")
    draw.text((left, top), text, textColor, font=fontText)
    return cv2.cvtColor(np.asarray(img), cv2.COLOR_RGB2BGR)


class Client(threading.Thread):
    def __init__(self, img_file, API_ID):
        threading.Thread.__init__(self)
        self.identity = uuid.uuid4().hex.encode()[0:16]
        self.img_file = img_file
        self.API_ID = API_ID

    def run(self):
        context = zmq.Context()
        socket = context.socket(zmq.DEALER)
        socket.setsockopt(zmq.IDENTITY, self.identity)
        # socket.connect('tcp://10.88.108.49:5550')
        socket.connect('tcp://127.0.0.1:5559')
        print('Client %s started\n' % self.identity)
        poll = zmq.Poller()
        poll.register(socket, zmq.POLLIN)
        img = cv2.imread(self.img_file)
        # img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        json_data = {
            'interface': self.API_ID,
            'api_key': '',
            'image_base64': [cv2_to_base64(img)],
            config.INTERFACE_THRESHOLD_DICT[self.API_ID][0]: config.INTERFACE_THRESHOLD_DICT[self.API_ID][1]
        }

        socket.send_json(json_data)
        # print( 'Req from client %s sent.\n' % self.identity)

        received_reply = False
        while not received_reply:
            sockets = dict(poll.poll(1000))
            if socket in sockets:
                if sockets[socket] == zmq.POLLIN:
                    msg = socket.recv_json()
                    # img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                    self.handle_result(msg, img)
                    print(f'Client with {self.img_file} and {self.identity} received reply: {msg}')
                    del msg
                    received_reply = True

        socket.close()
        context.term()

    def handle_result(self, msg, img):
        alarm_msg = msg[config.DETECTOR[self.API_ID]]
        print('if alarm_msg: ', alarm_msg != [])
        if alarm_msg != []:
            print(alarm_msg[0])
            if 'info' in alarm_msg[0]:
                for info in alarm_msg[0]['info']:
                    x1, y1, w, h = info['rect']
                    img = cv2ImgAddText(img, error_dict[self.API_ID], x1 + 20, y1 + 20)
                    # x1, y1, w, h = info['rect']
                    cv2.rectangle(img, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                    # cv2.putText(img, error_dict[self.API_ID], (100, 100), cv2.FONT_HERSHEY_COMPLEX, 2, (255, 0, 0), 2)
            elif 'people_info' in alarm_msg[0]:
                for info in alarm_msg[0]['people_info']:
                    # [{'0_smoke_result': 1, '1_left': 1232, '2_top': 310, '3_width': 135, '4_height': 270}]
                    x1, y1, w, h = info['1_left'], info['2_top'], info['3_width'], info['4_height']
                    img = cv2ImgAddText(img, error_dict[self.API_ID], x1 + 20, y1 + 20)
                    # x1, y1, w, h = info['rect']
                    cv2.rectangle(img, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                    # cv2.putText(img, error_dict[self.API_ID], (100, 100), cv2.FONT_HERSHEY_COMPLEX, 2, (255, 0, 0), 2)
            cv2.imwrite(save_img_dir + os.path.basename(self.img_file), img)


# if __name__ == "__main__":
# image_dir = 'helmet/ori_images/'
image_dir = 'data/images/'
interface = '38'
images = glob.glob(image_dir + '*.jpg') + glob.glob(image_dir + '*.png')
print(images[:5])
sep_images = list(split_list(images, NUMBER_OF_CLIENTS))
for i in range(NUMBER_OF_CLIENTS):
    for img_file in sep_images[i]:
        # if os.path.basename(img_file).startswith('c7803c1e-19f6-4a95-8dee-b785570af348'):
        client = Client(img_file, interface)
        client.start()
        print(img_file)
        time.sleep(1)
        # break
