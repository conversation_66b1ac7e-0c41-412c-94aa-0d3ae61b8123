#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
目标检测服务并发性能测试脚本。

功能:
- 支持配置服务器地址、端口、图像目录。
- 支持指定单个、多个或随机的检测接口 (API ID)。
- 支持配置并发数和总请求数。
- 为每个请求发送一个随机的检测阈值 (0.1-0.9)。
- 提供详细的性能统计报告，包括吞吐量、响应时间 (平均/最大/最小) 和成功率。

"""

import argparse
import base64
import glob
import os
import random
import threading
import time
import uuid
from collections import defaultdict
from threading import Lock, Thread

import cv2
import zmq

from api_config import CONFIG

def cv2_to_base64(image):
    data = cv2.imencode('.jpg', image)[1]
    return base64.b64encode(data.tobytes()).decode('utf8')

# --- 全局统计变量 ---
stats = defaultdict(lambda: {
    'total': 0,
    'success': 0,
    'failed': 0,
    'times': [],
    'errors': defaultdict(int)
})
global_stats = {
    'total': 0,
    'success': 0,
    'failed': 0,
    'times': [],
    'errors': defaultdict(int)
}
stats_lock = Lock()

# 接口ID与描述的映射关系
API_ID_DESC = {
    '38': '安全帽佩戴检测',
    '30': '烟火检测',
    '41': '工装穿戴检测',
    '35': '人员抽烟检测',
    '31': '区域入侵检测',
    '50': '安全带佩戴检测'
}

context = zmq.Context()

class TestClient(Thread):
    """模拟一个客户端请求的线程。"""
    def __init__(self, server_addr, server_port, image_path, interface_id, config):
        super().__init__()
        self.server_addr = server_addr
        self.server_port = server_port
        self.image_path = image_path
        self.interface_id = interface_id
        self.config = config
        self.identity = uuid.uuid4().hex.encode()[0:16]

    def run(self):
        start_time = time.time()
        error_msg = ""
        response = None
        
        try:
            socket = context.socket(zmq.DEALER)
            socket.setsockopt(zmq.LINGER, 0)
            socket.setsockopt(zmq.IDENTITY, self.identity)
            socket.connect(f'tcp://{self.server_addr}:{self.server_port}')

            img = cv2.imread(self.image_path)
            if img is None:
                raise Exception(f'无法读取图像: {self.image_path}')

            # --- 为请求生成随机阈值 ---
            request_data = {
                'interface': self.interface_id,
                'api_key': '',
                'image_base64': [cv2_to_base64(img)]
            }
            
            if self.interface_id == self.config.API_ID_SMOKE_FIRE:
                # 烟火检测，需要两个独立的随机阈值
                smoke_thresh = round(random.uniform(0.1, 0.9), 2)
                fire_thresh = round(random.uniform(0.1, 0.9), 2)
                request_data[self.config.PARAM_SMOKE_FIRE_SMOKE] = smoke_thresh
                request_data[self.config.PARAM_SMOKE_FIRE_FIRE] = fire_thresh
            else:
                # 其他检测，使用一个随机阈值
                threshold_key, _ = self.config.INTERFACE_THRESHOLD_DICT[self.interface_id]
                random_thresh = round(random.uniform(0.1, 0.9), 2)
                request_data[threshold_key] = random_thresh
            # --------------------------

            socket.send_json(request_data)
            
            poller = zmq.Poller()
            poller.register(socket, zmq.POLLIN)
            if poller.poll(30 * 1000):
                response = socket.recv_json()
            else:
                raise Exception('请求超时 (30秒)')

            if response.get('error_id') != 0:
                raise Exception(f"服务器错误: {response.get('error_message', '未知错误')} (ID: {response.get('error_id')})")

        except Exception as e:
            error_msg = str(e)
        finally:
            duration = (time.time() - start_time) * 1000
            with stats_lock:
                is_success = not error_msg
                
                global_stats['total'] += 1
                global_stats['times'].append(duration)
                if is_success:
                    global_stats['success'] += 1
                else:
                    global_stats['failed'] += 1
                    global_stats['errors'][error_msg] += 1
                
                stats[self.interface_id]['total'] += 1
                stats[self.interface_id]['times'].append(duration)
                if is_success:
                    stats[self.interface_id]['success'] += 1
                else:
                    stats[self.interface_id]['failed'] += 1
                    stats[self.interface_id]['errors'][error_msg] += 1
            
            if 'socket' in locals():
                socket.close()

def print_statistics(total_duration):
    """打印格式化的统计结果。"""
    if global_stats['total'] == 0:
        print("\n没有处理任何请求。")
        return

    success_rate = (global_stats['success'] / global_stats['total']) * 100
    avg_time = sum(global_stats['times']) / len(global_stats['times'])
    max_time = max(global_stats['times'])
    min_time = min(global_stats['times'])
    throughput = global_stats['total'] / total_duration if total_duration > 0 else 0

    print("\n" + "="*25 + " 全局测试统计 " + "="*25)
    print(f"总耗时: {total_duration:.2f} 秒")
    print(f"总请求数: {global_stats['total']}")
    print(f"成功率: {global_stats['success']}/{global_stats['total']} ({success_rate:.2f}%)")
    print(f"吞吐量 (RPS): {throughput:.2f} 请求/秒")
    print("\n响应时间 (毫秒):")
    print(f"  - 平均: {avg_time:.2f} ms")
    print(f"  - 最快: {min_time:.2f} ms")
    print(f"  - 最慢: {max_time:.2f} ms")

    if global_stats['errors']:
        print("\n错误类型分布:")
        for error, count in sorted(global_stats['errors'].items(), key=lambda item: item[1], reverse=True):
            print(f"  - [{count}次] {error}")

    if len(stats) > 1:
        print("\n" + "="*25 + " 按接口统计 " + "="*25)
        for interface_id, s in sorted(stats.items()):
            if s['total'] == 0: continue
            name = API_ID_DESC.get(interface_id, f'未知接口({interface_id})')
            s_rate = (s['success'] / s['total']) * 100
            avg_t = sum(s['times']) / len(s['times'])
            print(f"\n接口 {interface_id} ({name}):")
            print(f"  - 请求数: {s['total']}")
            print(f"  - 成功率: {s['success']}/{s['total']} ({s_rate:.2f}%)")
            print(f"  - 平均耗时: {avg_t:.2f} ms")
    print("\n" + "="*60 + "\n")

def main():
    parser = argparse.ArgumentParser(description='目标检测服务并发性能测试工具。')
    parser.add_argument('--address', default='127.0.0.1', help='服务器地址')
    parser.add_argument('--port', default='5559', help='服务器端口')
    parser.add_argument('--image-dir', default='data/images', help='测试图像文件夹路径')
    parser.add_argument('--interface', help=f'指定单个接口ID进行测试。可选: {list(API_ID_DESC.keys())}')
    parser.add_argument('--random-interface', action='store_true', help='从所有可用接口中随机选择进行测试')
    parser.add_argument('--interfaces', help='指定接口ID列表(逗号分隔, 如 "38,35"), 将在这些接口中随机选择')
    parser.add_argument('--concurrency', type=int, default=20, help='并发请求数')
    parser.add_argument('--total-requests', type=int, default=300, help='总请求数')
    parser.add_argument('--random-image', action='store_true', help='随机选择图像发送，否则按顺序循环选择')
    args = parser.parse_args()

    if args.concurrency > 100:
        print("警告: 并发数 > 100, 可能导致客户端或服务器资源问题。")

    image_paths = glob.glob(os.path.join(args.image_dir, '*.jpg')) + \
                  glob.glob(os.path.join(args.image_dir, '*.png'))
    if not image_paths:
        print(f"错误: 在目录 '{args.image_dir}' 中未找到任何 .jpg 或 .png 图像。")
        return

    if args.interface:
        available_interfaces = [args.interface]
    elif args.interfaces:
        available_interfaces = args.interfaces.split(',')
    else:
        available_interfaces = list(API_ID_DESC.keys())
    
    for i in available_interfaces:
        if i not in API_ID_DESC:
            print(f"错误: 无效的接口ID '{i}'。有效ID为: {list(API_ID_DESC.keys())}")
            return

    print(f"测试准备就绪:")
    print(f"  - 服务器: tcp://{args.address}:{args.port}")
    print(f"  - 并发数: {args.concurrency}")
    print(f"  - 总请求: {args.total_requests}")
    print(f"  - 测试接口: {available_interfaces}")
    print(f"  - 图像来源: {len(image_paths)} 张图片来自 '{args.image_dir}'")
    print("  - 阈值: 每个请求随机 (0.1-0.9)")

    config = CONFIG()
    tasks = []
    for i in range(args.total_requests):
        img_path = random.choice(image_paths) if args.random_image else image_paths[i % len(image_paths)]
        iface_id = random.choice(available_interfaces)
        tasks.append((args.address, args.port, img_path, iface_id, config))

    start_time = time.time()
    threads = []
    semaphore = threading.Semaphore(args.concurrency)

    print("\n开始发送请求...")
    for task_params in tasks:
        semaphore.acquire()
        client_thread = TestClient(*task_params)
        threads.append(client_thread)
        client_thread.start()

        def release_on_completion(thread, sem):
            thread.join()
            sem.release()
        
        Thread(target=release_on_completion, args=(client_thread, semaphore)).start()

    for t in threads:
        t.join()

    total_duration = time.time() - start_time
    print("所有请求处理完毕。")

    print_statistics(total_duration)

    context.term()

if __name__ == "__main__":
    main()