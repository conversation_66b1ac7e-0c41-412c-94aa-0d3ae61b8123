# -*- coding: utf-8 -*-
"""
Created on Mon May  9 17:11:02 2022

@author: <PERSON>
"""

import argparse
import copy
import json
import logging.config
import os

import threading
import time
import uuid

import cv2
import zmq

from utils.algorithm import generel_detect, person_smoke_detect
from utils.api_config import CONFIG
from utils.detect import HubDetection, PoseDetection
from utils.tool import get_logger, error_id_handler, cut_max_rec
from utils.torch_utils import time_sync

# log_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'utils', 'logging.conf')
log_file_path = 'utils/logging.conf'
logging.config.fileConfig(log_file_path)
debuglogger = logging.getLogger(name='debuglog')
errorlogger = logging.getLogger(name='errorlog')

previous_poses = []

API_ID_dict = {'38': 'helmet',
               # '30': 'fire_smoke',  # 暂不保存入侵图片
               '41': 'workcloth',
               '35': 'smoking',
               # '31': 'invade',  # 暂不保存入侵图片
               '50': 'safebelt'}


def save_different_images(request_id, alarm_type, img, ori_img, render_image):
    date = time.strftime('%Y-%m-%d', time.localtime())
    root = 'save_result/'
    alarm_type = root + alarm_type
    os.makedirs(alarm_type, exist_ok=True)
    alarm_imgs_dir = alarm_type + '/' + date + '/alarm_images/'
    ori_images_dir = alarm_type + '/' + date + '/ori_images/'
    render_images_dir = alarm_type + '/' + date + '/render_images/'
    os.makedirs(alarm_imgs_dir, exist_ok=True)
    os.makedirs(ori_images_dir, exist_ok=True)
    os.makedirs(render_images_dir, exist_ok=True)
    cv2.imwrite(alarm_imgs_dir + '/' + request_id + '.jpg', img)
    cv2.imwrite(ori_images_dir + '/' + request_id + '.jpg', ori_img)
    cv2.imwrite(render_images_dir + '/' + request_id + '.jpg', render_image)
    debuglogger.info(f'saved img {request_id}')


class Server(threading.Thread):
    def __init__(self, port, device):
        self._stop = threading.Event()
        self.device = device
        # 预测模型地址
        self.HELMET_MODEL = HubDetection(weights=config.HELMET_PATH, device=self.device)
        self.SMOKE_FIRE_MODEL = HubDetection(weights=config.SMOKE_FIRE_PATH, device=self.device)
        self.UNIFORM_MODEL = HubDetection(weights=config.UNIFORM_PATH, device=self.device)
        self.SMOKE_MODEL = HubDetection(weights=config.SMOKE_PATH, device=self.device)
        self.SMOKE_POSE_MODEL = PoseDetection(weights=config.SMOKE_POSE_PATH)
        self.INVADE_MODEL = HubDetection(weights=config.INVADE_PATH, device=self.device)
        self.SAFEBELT_MODEL = HubDetection(weights=config.SAFEBELT_PATH, device=self.device)
        self.MODEL_DICT = {
            config.API_ID_HELMET: self.HELMET_MODEL,
            config.API_ID_SMOKE_FIRE: self.SMOKE_FIRE_MODEL,
            config.API_ID_UNIFORM: self.UNIFORM_MODEL,
            config.API_ID_SMOKE: self.SMOKE_MODEL,
            config.API_ID_INVADE: self.INVADE_MODEL,
            config.API_ID_SAFEBELT: self.SAFEBELT_MODEL
        }
        self.interface_threshold_dict = config.INTERFACE_THRESHOLD_DICT
        # 服务器ip
        self.SERVER_IP = config.SERVER_IP
        self.SERVER_PORT = port
        self.t1 = int(time.time())
        threading.Thread.__init__(self)

    def stop(self):
        self._stop.set()

    def stopped(self):
        return self._stop.is_set()

    def run(self):
        context = zmq.Context()
        frontend = context.socket(zmq.ROUTER)
        # frontend.bind(self.SERVER_IP)   'tcp://*:5559'
        frontend.bind('tcp://*:%i' % (self.SERVER_PORT))

        backend = context.socket(zmq.DEALER)
        backend.bind('inproc://backend')

        poll = zmq.Poller()
        poll.register(frontend, zmq.POLLIN)
        poll.register(backend, zmq.POLLIN)

        while not self.stopped():
            try:
                sockets = dict(poll.poll(1000))
                if frontend in sockets:
                    if sockets[frontend] == zmq.POLLIN:
                        _id = frontend.recv()
                        msg = frontend.recv()
                        # print( 'Server received %s\n' % _id)
                        # print( 'Server received %s\n' % msg)
                        msg = self.get_results(msg, _id)
                        handler = RequestHandler(context, _id, msg)
                        handler.start()

                if backend in sockets:
                    if sockets[backend] == zmq.POLLIN:
                        _id = backend.recv()
                        msg = backend.recv()
                        # print( 'Server sending to frontend %s\n' % msg)
                        frontend.send(_id, zmq.SNDMORE)
                        frontend.send(msg)
            except Exception as e:
                errorlogger.exception(e)

        frontend.close()
        backend.close()
        context.term()

    def get_results(self, msg, _id):
        t1 = time_sync()
        json_data = json.loads(msg)
        debuglogger.info(f'interface : {json_data.get("interface")}')
        handle_result = error_id_handler(json_data)
        if type(handle_result) == int:
            if handle_result == 10012:
                interface = json_data.get("interface")
                if interface == config.API_ID_INVADE:
                    t2 = time_sync()
                    msg = {
                        "error_id": 0,
                        "error_message": config.ERROR_MESSAGE[interface],
                        config.DETECTOR[interface]: [],
                        "request_id": str(uuid.uuid4()),
                        "time_used": int((t2 - t1) * 1000)
                    }
            else:
                msg = {'error_id': handle_result}
        else:  # 接收到正常图片, 进行推理
            image_bgr = handle_result  # for imwrite and pose detection
            interface = json_data.get("interface")  # MODEL API ID

            # 获取所有接口的threshold
            if interface == config.API_ID_SMOKE_FIRE:  # 烟火threshold
                smoke_thres = json_data.get(config.PARAM_SMOKE_FIRE_SMOKE)
                fire_thres = json_data.get(config.PARAM_SMOKE_FIRE_FIRE)
                if smoke_thres and fire_thres:
                    smoke_threshold = smoke_thres
                    fire_threshold = fire_thres
                else:  # 如果没有收到完整的烟火阈值，则使用系统默认值
                    smoke_threshold = self.interface_threshold_dict[interface][
                        config.PARAM_SMOKE_FIRE_SMOKE]
                    fire_threshold = self.interface_threshold_dict[interface][config.PARAM_SMOKE_FIRE_FIRE]
                threshold = (smoke_threshold, fire_threshold)
            else:
                # 其他模型threshold
                thres = json_data.get(self.interface_threshold_dict[interface][0])
                # print('threshold:',self.interface_threshold_dict[interface][1])
                if thres:
                    threshold = thres
                else:
                    threshold = self.interface_threshold_dict[interface][1]
                # print('threshold:', threshold)
                threshold = (threshold,)

            # 添加日志记录最终使用的阈值
            logger.info(f'Request from {_id} for interface {interface} is using threshold: {threshold}')

            # print('interface:', interface)

            # 加载模型
            model = self.MODEL_DICT[interface]
            image_rgb = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB)  # for object detection
            model.inference(image_rgb)
            render_image = model.get_render_image()
            render_image = cv2.cvtColor(render_image, cv2.COLOR_RGB2BGR)
            image_bgr_for_posedrawing = copy.deepcopy(image_bgr)
            data = model.get_data()

            # 控制保存有人的图片，有人即保存一帧，间隔60秒，没人则间隔30秒
            # if args.save_image:
            #     save_image(person_model, image_bgr, self.t1, interval=30)

            res = str(data)
            debuglogger.info(f'infer result: {res}')
            if interface == config.API_ID_SMOKE:
                debuglogger.info(f'detect res: {res}')
                info = eval(res)
                det_bboxes = [i for i in info if i['class'] in config.FILTER_IDS[interface]]
                valid_detbox_len = len([i for i in det_bboxes if i['conf'] >= threshold[0]])
                if valid_detbox_len:
                    pose_model = self.SMOKE_POSE_MODEL
                    crop_img, offset_x, offset_y = cut_max_rec(det_bboxes, 1, image_bgr, threshold)
                    heatmaps, pafs, scale, pad = pose_model.inference(crop_img)
                    pose_data = pose_model.get_data(heatmaps, pafs, scale, pad,
                                                    offset_x, offset_y, previous_poses)
                    res = person_smoke_detect(image_bgr_for_posedrawing, pose_data, det_bboxes, threshold,
                                              alarm_angle=40)

                else:
                    res = []
            else:
                res = generel_detect(image_rgb, eval(res), interface, *threshold)
            t2 = time_sync()
            msg = {
                "error_id": 0,
                "error_message": config.ERROR_MESSAGE[interface],
                config.DETECTOR[interface]: res,
                "request_id": str(uuid.uuid4()),
                "time_used": int((t2 - t1) * 1000)
            }
            if args.save_result:
                self.save_result(msg, image_bgr, render_image, image_bgr_for_posedrawing, interface)
            logger.info(
                f'Server received message: Client_id: {_id}, interface: {interface}, threshold: {threshold}, msg: {msg} ')
        return msg

    def save_result(self, msg, img, render_image, img_for_posedrawing, API_ID):
        alarm_msg = msg[config.DETECTOR[API_ID]]
        ori_img = copy.deepcopy(img)

        if alarm_msg and API_ID in API_ID_dict:
            debuglogger.info(f'alarm_msg: {alarm_msg}')
            alarm_type = API_ID_dict[API_ID]
            request_id = msg['request_id']  # unique name for image saving
            if isinstance(alarm_msg, list):
                if 'info' in alarm_msg[0]:  # helment
                    for info in alarm_msg[0]['info']:
                        x1, y1, w, h = info['rect']
                        cv2.rectangle(img, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                        save_different_images(request_id, alarm_type, img, ori_img, render_image)

                elif 'people_info' in alarm_msg[0]:
                    for info in alarm_msg[0]['people_info']:
                        if '0_wear_result' in info:  # 工服检测只有0表示未穿戴工服
                            if info['0_wear_result'] == 0:
                                # [{'0_smoke_result': 1, '1_left': 1232, '2_top': 310, '3_width': 135, '4_height': 270}]
                                x1, y1, w, h = info['1_left'], info['2_top'], info['3_width'], info['4_height']
                                cv2.rectangle(img, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                                save_different_images(request_id, alarm_type, img, ori_img, render_image)
                        if '0_smoke_result' in info:  # 抽烟检测1表示抽烟
                            if info['0_smoke_result'] == 1:
                                # [{'0_smoke_result': 1, '1_left': 1232, '2_top': 310, '3_width': 135, '4_height': 270}]
                                x1, y1, w, h = info['1_left'], info['2_top'], info['3_width'], info['4_height']
                                cv2.rectangle(img_for_posedrawing, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                                save_different_images(request_id, alarm_type, img_for_posedrawing, ori_img,
                                                      render_image)

            elif isinstance(alarm_msg, dict):  # safebel model
                for box in alarm_msg['info']:
                    x1, y1, w, h = box['rect']
                    cv2.rectangle(img, (x1, y1), (x1 + w, y1 + h), (0, 0, 255), 1)
                    save_different_images(request_id, alarm_type, img, ori_img, render_image)


class RequestHandler(threading.Thread):
    def __init__(self, context, id, msg):
        """
        RequestHandler
        :param context: ZeroMQ context
        :param id: Requires the identity frame to include in the reply so that it will be properly routed
        :param msg: Message payload for the worker to process
        """
        threading.Thread.__init__(self)
        self.context = context
        self.msg = msg
        self._id = id

    def run(self):
        # Worker will process the task and then send the reply back to the DEALER backend socket via inproc
        worker = self.context.socket(zmq.DEALER)
        worker.connect('inproc://backend')
        # logger.info('\nRequest handler started to process.')

        # Simulate a long-running operation
        # time.sleep(PROCESSING_TIME)

        worker.send(self._id, zmq.SNDMORE)
        worker.send_json(self.msg)
        # logger.info( 'Server %s send reply: %s' % (self._id, self.msg))
        logger.info(f'Server send reply: Client_id: {self._id}, msg: {self.msg}')
        del self.msg

        logger.info('Request handler quitting.\n')
        worker.close()


def main(port, device):
    # Start the server that will handle incoming requests
    server = Server(port, device)
    server.start()
    logger.info('Server Starting. \nWaiting for Client Connect.')


if __name__ == "__main__":
    config = CONFIG()

    parser = argparse.ArgumentParser(description="port")
    # 添加参数
    parser.add_argument("-p", "--port", help="port", default=5559, type=int)
    parser.add_argument("-g", "--gpu", help="gpu", default=0, type=int)
    parser.add_argument("-l", "--log", help="log", action='store_true', default=True)
    parser.add_argument("-s", "--save_image", help="save_image", action='store_true', default=True)
    parser.add_argument("-sr", "--save_result", help="save_result", action='store_true', default=True)
    args = parser.parse_args()

    logger = get_logger('./logs', args.log)
    # if args.save_image:
    #     person_model = HubDetection(weights='weights/yolov5s.pt', device=args.gpu, classes=[0], conf=0.6)
    main(args.port, args.gpu)
