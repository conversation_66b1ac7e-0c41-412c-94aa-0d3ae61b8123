# -*- coding: utf-8 -*-
"""
Created on Mon Apr 18 10:08:07 2022

@author: Z20070346
"""

import os
import sys
from pathlib import Path

import numpy as np
import torch

from models.with_mobilenet import PoseEstimationWithMobileNet
from modules.keypoints import extract_keypoints, group_keypoints
from modules.load_state import load_state
from modules.pose import Pose, track_poses
from utils.tool import normalize, pad_width

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative

from utils.general import (LOGGER, cv2)
from utils.torch_utils import time_sync
import logging.config

debuglogger = logging.getLogger(name='debuglog')
errorlogger = logging.getLogger(name='errorlog')


class HubDetection:
    def __init__(self,
                 weights='yolov5s.pt',
                 # r'D:\yolov5\yolov5\runs\train\exp7\weights\best.pt',#'model/hat_311.pt',    # hat_28294.pt  yolov5s.pt
                 # source='data\images',
                 data='data/coco128.yaml',
                 imgsz=640,
                 conf=0.25,  # NMS confidence threshold
                 iou=0.45,  # NMS IoU threshold
                 agnostic=False,  # NMS class-agnostic, 
                 multi_label=False,  # NMS multiple labels per box
                 classes=None,
                 max_det=300,  # maximum number of detections per image
                 amp=True,  # Automatic Mixed Precision (AMP) inference
                 device=''
                 ):
        self.weights = weights
        # self.source = source
        self.data = data
        self.imgsz = imgsz
        self.conf = conf
        self.iou = iou
        self.agnostic = agnostic
        self.multi_label = multi_label
        self.classes = classes  # (optional list) filter by class, i.e. = [0, 15, 16] for COCO persons, cats and dogs
        self.max_det = max_det
        self.amp = amp
        self.device = device
        self.model = self.load_model()
        # self.results = None
        self.inference_time = None

    def load_model(self):
        print(self.device)

        # self.model = torch.hub.load('', 'custom', path = self.weights, source = 'local').eval().to(select_device(self.device))
        self.model = torch.hub.load('', 'custom', path=self.weights, source='local', device=self.device,
                                    force_reload=False)
        self.model.conf = self.conf
        self.model.iou = self.iou
        self.model.agnostic = self.agnostic
        self.model.multi_label = self.multi_label
        self.model.classes = self.classes
        self.model.max_det = self.max_det
        self.model.amp = self.amp
        debuglogger.info(f'loaded {self.weights} to gpu-{self.device}')
        return self.model

    def inference(self, image):
        '''
        # image is file, Path, PIL, OpenCV, numpy, list
        '''
        t1 = time_sync()
        self.results = self.model(image, size=self.imgsz)  # custom inference size
        t2 = time_sync()
        self.inference_time = t2 - t1
        LOGGER.info(f'Done. ({t2 - t1:.3f}s)')
        return self.results

    def get_data(self):
        '''
        [{'label': 'tv',
          'conf': 0.7899675369262695,
          'box': [172, 360, 371, 611],
          'class': 62},
         {'label': 'potted plant',
          'conf': 0.6791136860847473,
          'box': [565, 491, 632, 631],
          'class': 58},
         ]
        '''
        self.data = self.results.pandas().xyxy[0]

        data = []
        for i in range(len(self.data)):
            dic = {}
            # print(self.data.loc[i])
            dic['label'] = self.data.loc[i]['name']
            dic['conf'] = float(self.data.loc[i]['confidence'])
            dic['box'] = [int(self.data.loc[i]['xmin']),
                          int(self.data.loc[i]['ymin']),
                          int(self.data.loc[i]['xmax']),
                          int(self.data.loc[i]['ymax'])]
            dic['class'] = int(self.data.loc[i]['class'])
            data.append(dic)
        return data

    def get_ori_image(self):
        '''
        获取原图像 array
        '''
        self.ori_image = self.results.imgs[0]
        return self.ori_image

    def get_render_image(self):
        '''
        获取带预测框和预测label的图像 array
        '''
        self.render_image = self.results.render()[0]
        return self.render_image

    def get_pandas(self):
        '''
        获取预测结果dataframe
        '''
        self.pandas_df = self.results.pandas().xyxy[0]
        return self.pandas_df

    def get_json(self):
        '''
        获取预测结果json
        '''
        self.json = self.results.pandas().xyxy[0].to_json(orient="records")
        return self.json

    def get_inference_time(self):
        '''
        获取预测使用的时间
        '''
        return int(self.inference_time * 1000)


class PoseDetection:
    def __init__(self,
                 weights='weights/cigar.pth',
                 height_size=256,
                 smooth=1,
                 stride=8,
                 upsample_ratio=4,
                 cpu=False,
                 pad_value=(0, 0, 0),
                 img_mean=np.array([128, 128, 128], np.float32),
                 img_scale=np.float32(1 / 256),
                 track=1
                 ):
        self.weights = weights
        self.height_size = height_size
        self.smooth = smooth
        self.stride = stride
        self.upsample_ratio = upsample_ratio
        self.cpu = cpu
        self.pad_value = pad_value
        self.img_mean = img_mean
        self.img_scale = img_scale
        self.track = track
        self.model = self.load_model()

    def load_model(self):
        self.model = PoseEstimationWithMobileNet()
        checkpoint = torch.load(self.weights)
        load_state(self.model, checkpoint)
        self.model = self.model.eval()
        if not self.cpu:
            self.model = self.model.cuda()
        debuglogger.info(f'loaded posemodel')
        return self.model

    def inference(self, img):
        height, width, _ = img.shape
        scale = self.height_size / height

        scaled_img = cv2.resize(img, (0, 0), fx=scale, fy=scale, interpolation=cv2.INTER_LINEAR)  # fx,fy为缩放因子
        scaled_img = normalize(scaled_img, self.img_mean, self.img_scale)
        min_dims = [self.height_size, max(scaled_img.shape[1], self.height_size)]
        padded_img, pad = pad_width(scaled_img, self.stride, self.pad_value, min_dims)

        tensor_img = torch.from_numpy(padded_img).permute(2, 0, 1).unsqueeze(0).float()
        if not self.cpu:
            tensor_img = tensor_img.cuda()
        stages_output = self.model(tensor_img)

        stage2_heatmaps = stages_output[-2]
        heatmaps = np.transpose(stage2_heatmaps.squeeze().cpu().data.numpy(), (1, 2, 0))
        heatmaps = cv2.resize(heatmaps, (0, 0), fx=self.upsample_ratio, fy=self.upsample_ratio,
                              interpolation=cv2.INTER_CUBIC)

        stage2_pafs = stages_output[-1]
        pafs = np.transpose(stage2_pafs.squeeze().cpu().data.numpy(), (1, 2, 0))
        pafs = cv2.resize(pafs, (0, 0), fx=self.upsample_ratio, fy=self.upsample_ratio, interpolation=cv2.INTER_CUBIC)

        return heatmaps, pafs, scale, pad

    def get_data(self, heatmaps, pafs, scale, pad, offset_x, offset_y, previous_poses):
        total_keypoints_num = 0
        all_keypoints_by_type = []
        num_keypoints = Pose.num_kpts
        for kpt_idx in range(num_keypoints):  # 一共18个关键点，19th for background
            total_keypoints_num += extract_keypoints(heatmaps[:, :, kpt_idx], all_keypoints_by_type,
                                                     total_keypoints_num)

        print('total_keypoints_num', total_keypoints_num)
        pose_entries, all_keypoints = group_keypoints(all_keypoints_by_type, pafs)
        # print('pose_entries, all_keypoints',pose_entries, all_keypoints)
        for kpt_id in range(all_keypoints.shape[0]):
            all_keypoints[kpt_id, 0] = (all_keypoints[kpt_id, 0] * self.stride / self.upsample_ratio - pad[1]) / scale
            all_keypoints[kpt_id, 1] = (all_keypoints[kpt_id, 1] * self.stride / self.upsample_ratio - pad[0]) / scale
        current_poses = []
        for n in range(len(pose_entries)):
            if len(pose_entries[n]) == 0:
                continue
            pose_keypoints = np.ones((num_keypoints, 2), dtype=np.int32) * -1
            for kpt_id in range(num_keypoints):
                if pose_entries[n][kpt_id] != -1.0:  # keypoint was found
                    pose_keypoints[kpt_id, 0] = int(all_keypoints[int(pose_entries[n][kpt_id]), 0] + offset_x)
                    pose_keypoints[kpt_id, 1] = int(all_keypoints[int(pose_entries[n][kpt_id]), 1] + offset_y)
            pose = Pose(pose_keypoints, pose_entries[n][18])
            current_poses.append(pose)
        if self.track:
            track_poses(previous_poses, current_poses, smooth=self.smooth)
            previous_poses = current_poses
        print('current_poses', current_poses)
        return current_poses
