# -*- coding: utf-8 -*-
"""
Created on Mon May  9 17:11:02 2022

@author: Z20070346
"""

import base64
import copy
import logging
import logging.config
import math
import os

import cv2
import numpy as np
import torch

from utils.api_config import CONFIG
from utils.general import xyxy2xywh, xywh2xyxy, clip_coords

os.makedirs('Logs', exist_ok=True)
debuglogger = logging.getLogger(name='debuglog')
errorlogger = logging.getLogger(name='errorlog')

config = CONFIG()


def get_logger(path, log):
    """
    creates logger instance. writing out info to file and to terminal.
    :param path: experiment directory, where logger.log file is stored.
    :return: logger instance.
    """
    if not os.path.exists(path):
        os.mkdir(path)
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)  # Log等级总开关
    if log:
        log_file_abs = path + '/logger.log'

        handler = logging.FileHandler(log_file_abs, mode='w', encoding='UTF-8')  # 日志文件输出
        handler.setLevel(logging.DEBUG)

        # 文件输出格式
        logging_format = logging.Formatter(
            '%(asctime)s - %(levelname)s: %(message)s')

        handler.setFormatter(logging_format)  # 为改处理器handler 选择一个格式化器
        # stream_handler.setFormatter(stream_format)

        logger.addHandler(handler)  # 为记录器添加 处理方式Handler
        # logger.addHandler(stream_handler)
    # 控制台输出格式
    stream_handler = logging.StreamHandler()  # 日志控制台输出
    stream_format = logging.Formatter("%(asctime)s - %(levelname)s: %(message)s")
    logger.info("|------logger.info-----")

    return logger


def check_api_key(json_data):  #
    # interface = json_data.get("interface")
    api_key = json_data.get("api_key")
    if api_key == config.API_KEY:
        return True
    else:
        return False


def base64_cv2(base64_str):
    imgString = base64.b64decode(base64_str)
    nparr = np.frombuffer(imgString, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    return image


def cv2_to_base64(image):
    data = cv2.imencode('.jpg', image)[1]
    return base64.b64encode(data.tobytes()).decode('utf8')


def error_id_handler(json_data):
    error_id = 0
    api_key = check_api_key(json_data)

    # print(api_key)
    # print('json_data.get("interface")', not (json_data.get("interface")
    #           and json_data.get("image_base64")))
    # print('in list', not json_data.get("interface") in config.API_ID_LIST)
    if not api_key:
        error_id = 10001  # API Key错误
        return error_id
    elif not json_data.get("interface"):
        error_id = 10005  # Json参数错误
        return error_id
    elif not json_data.get("interface") in config.API_ID_LIST:
        error_id = 10011  # API ID错误
        return error_id
    try:
        image_base64 = json_data.get("image_base64")[0]
    # base64转cv2
    except:
        error_id = 10012  # 图片为空
        return error_id
    try:
        image = base64_cv2(image_base64)
    except:
        error_id = 10007  # 图片base64编码错误
        return error_id
    # else:
    #     error_id = 10004  # 未知错误
    # print('error_id', error_id)
    return image if error_id == 0 else error_id


def cut_max_rec(det_bboxes, gain, orig_img, threshold):
    xyxy = np.array([i['box'] for i in det_bboxes])
    x0 = xyxy[:, 0].min()
    y0 = xyxy[:, 1].min()
    x1 = xyxy[:, 2].max()
    y1 = xyxy[:, 3].max()
    xyxy_crop = torch.tensor([[x0, y0, x1, y1]])
    img_for_blackpadding = copy.deepcopy(orig_img)

    b = xyxy2xywh(xyxy_crop)  # boxes
    b[:, 2:] = b[:, 2:] * gain + 10  # box wh * gain + pad
    xyxy_crop = xywh2xyxy(b).long()
    clip_coords(xyxy_crop, orig_img.shape)
    for det_bbox in det_bboxes:
        if det_bbox['conf'] < threshold[0]:
            x0, y0, x1, y1 = det_bbox['box']
            img_for_blackpadding[int(y0):int(y1), int(x0):int(x1)] = 0
            # cv2.putText(orig_img, str(det_bbox['conf']), (int(x0), int(y0)), cv2.FONT_HERSHEY_COMPLEX, 2, (255, 0, 0), 2)

    crop = img_for_blackpadding[int(xyxy_crop[0, 1]):int(xyxy_crop[0, 3]), int(xyxy_crop[0, 0]):int(xyxy_crop[0, 2]),
           ::1]
    offset_x = xyxy_crop[0][0].numpy()
    offset_y = xyxy_crop[0][1].numpy()
    return crop, offset_x, offset_y


def normalize(img, img_mean, img_scale):
    img = np.array(img, dtype=np.float32)
    img = (img - img_mean) * img_scale
    return img


def pad_width(img, stride, pad_value, min_dims):
    h, w, _ = img.shape
    h = min(min_dims[0], h)
    min_dims[0] = math.ceil(min_dims[0] / float(stride)) * stride
    min_dims[1] = max(min_dims[1], w)
    min_dims[1] = math.ceil(min_dims[1] / float(stride)) * stride
    pad = []
    pad.append(int(math.floor((min_dims[0] - h) / 2.0)))
    pad.append(int(math.floor((min_dims[1] - w) / 2.0)))
    pad.append(int(min_dims[0] - h - pad[0]))
    pad.append(int(min_dims[1] - w - pad[1]))
    padded_img = cv2.copyMakeBorder(img, pad[0], pad[2], pad[1], pad[3],
                                    cv2.BORDER_CONSTANT, value=pad_value)
    return padded_img, pad


def save_image(person_model, image, t1, interval=60):
    import time, uuid, os
    t2 = int(time.time())
    path = 'save_image/'
    date = time.strftime('%Y-%m-%d', time.localtime())
    data_path = path + date
    if not os.path.exists(data_path):
        os.mkdir(data_path)
    if (t2 - t1) % interval == 0:
        person_model.inference(image)
        data = person_model.get_data()
        if data != []:
            cv2.imwrite(data_path + '/' + str(uuid.uuid4()) + '.jpg', image)  # 存储为图像
            interval = 60
        else:
            interval = 30


def compute_IOself(rec1, rec2):
    """
    自定义：计算两个矩形框的交并比。
    :param rec1: (x0,y0,x1,y1)      (x0,y0)代表矩形左上的顶点，（x1,y1）代表矩形右下的顶点。下同。
    :param rec2: (x0,y0,x1,y1)
    :return: IOU = (area(rec1) ∩ area(rec2)) /area(rec1)
    """
    left_column_max = max(rec1[0], rec2[0])
    right_column_min = min(rec1[2], rec2[2])
    up_row_max = max(rec1[1], rec2[1])
    down_row_min = min(rec1[3], rec2[3])
    # 两矩形无相交区域的情况
    if left_column_max >= right_column_min or down_row_min <= up_row_max:
        return 0
    # 两矩形有相交区域的情况
    else:
        S1 = (rec1[2] - rec1[0]) * (rec1[3] - rec1[1])
        S2 = (rec2[2] - rec2[0]) * (rec2[3] - rec2[1])
        S_cross = (down_row_min - up_row_max) * (right_column_min - left_column_max)
        return S_cross / S1


def compute_IOU(rec1, rec2):
    """
    自定义：计算两个矩形框的交并比。
    :param rec1: (x0,y0,x1,y1)      (x0,y0)代表矩形左上的顶点，（x1,y1）代表矩形右下的顶点。下同。
    :param rec2: (x0,y0,x1,y1)
    :return: IOU = (area(rec1) ∩ area(rec2)) /area(rec1)
    """
    eps = 1e-7
    left_column_max = max(rec1[0], rec2[0])
    right_column_min = min(rec1[2], rec2[2])
    up_row_max = max(rec1[1], rec2[1])
    down_row_min = min(rec1[3], rec2[3])
    # 两矩形无相交区域的情况
    if left_column_max >= right_column_min or down_row_min <= up_row_max:
        return 0
    # 两矩形有相交区域的情况
    else:
        S1 = (rec1[2] - rec1[0]) * (rec1[3] - rec1[1])
        S2 = (rec2[2] - rec2[0]) * (rec2[3] - rec2[1])
        # Intersection area
        S_cross = (down_row_min - up_row_max) * (right_column_min - left_column_max)
    w1, h1 = rec1[2] - rec1[0], rec1[3] - rec1[1] + eps
    w2, h2 = rec2[2] - rec2[0], rec2[3] - rec2[1] + eps
    # Union Area
    union = w1 * h1 + w2 * h2 - S_cross + eps
    return S_cross / union


def posebox2yolobox(yoloboxes, posebox, threshold):
    yoloboxes = [i['box'] for i in yoloboxes if i['conf'] >= threshold[0]]
    ious = []
    for i, box in enumerate(yoloboxes):
        ious.append(compute_IOU(box, posebox))
    return yoloboxes[ious.index(max(ious))]


class Point:
    """
    2D坐标点
    """

    def __init__(self, x, y):
        self.X = x
        self.Y = y


class Line:
    def __init__(self, point1, point2):
        """
        初始化包含两个端点
        :param point1:
        :param point2:
        """
        self.Point1 = point1
        self.Point2 = point2


def GetAngle(line1, line2):
    """
    计算两条线段之间的夹角
    :param line1:
    :param line2:
    :return:
    """
    dx1 = line1.Point1.X - line1.Point2.X
    dy1 = line1.Point1.Y - line1.Point2.Y
    dx2 = line2.Point1.X - line2.Point2.X
    dy2 = line2.Point1.Y - line2.Point2.Y
    angle1 = math.atan2(dy1, dx1)
    angle1 = int(angle1 * 180 / math.pi)
    # print(angle1)
    angle2 = math.atan2(dy2, dx2)
    angle2 = int(angle2 * 180 / math.pi)
    # print(angle2)
    if angle1 * angle2 >= 0:
        insideAngle = abs(angle1 - angle2)
    else:
        insideAngle = abs(angle1) + abs(angle2)
        if insideAngle > 180:
            insideAngle = 360 - insideAngle
    insideAngle = insideAngle % 180
    return insideAngle


def single_xyxy2xywh(box):
    x1, y1, x2, y2 = box
    w = x2 - x1
    h = y2 - y1
    return [x1, y1, w, h]


def filter_area(det_bboxes, minArea, filter_area_classid):
    bboxes = []
    for box in det_bboxes:
        if box["class"] in filter_area_classid:
            area = (box["box"][2] - box["box"][0]) * (box["box"][3] - box["box"][1])
            if area > minArea:
                bboxes.append(box)
        else:
            bboxes.append(box)
    return bboxes
