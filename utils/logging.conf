# recorder: provide interface for us
# to set recorder name, root is compulsory
[loggers]
keys = root, debuglog, errorlog

# handlers: set logs from recorder to destination
# to set type of handler
[handlers]
keys=fileHandler1, fileHandler2, consoleHandler

[formatters]
keys=simpleFormatter

# set level and type of recorder root
[logger_root]
level=DEBUG
handlers=consoleHandler

[logger_debuglog]
level=DEBUG
handlers=fileHandler1,consoleHandler
# used as para for getlogger
qualname=debuglog
# heritage relation
propagate=0

# set level and type of recorder errorlog
[logger_errorlog]
level=DEBUG
handlers=fileHandler1,fileHandler2,consoleHandler
# used as para for getlogger
qualname=errorlog
# heritage relation
propagate=0


[handler_consoleHandler]
class=StreamHandler
args=(sys.stdout,)
level=DEBUG
formatter=simpleFormatter

[handler_fileHandler1]
class=handlers.TimedRotatingFileHandler
# name, time for start a new log, delay after midnight, the last 0 means to keep historical files
args=('Logs/debug.log','midnight',1,0)
level=DEBUG
formatter=simpleFormatter

[handler_fileHandler2]
class=handlers.TimedRotatingFileHandler
# name, time for start a new log, delay after midnight, the last 0 means to keep historical files
args=('Logs/error.log','midnight',1,0)
level=WARNING
formatter=simpleFormatter


[formatter_simpleFormatter]
format=%(asctime)s|%(levelname)8s|%(filename)s[:%(lineno)d]|%(message)s

datefmt=%Y-%m-%d %H:%M:%S
