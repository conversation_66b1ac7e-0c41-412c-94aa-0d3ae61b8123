#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Fri May  6 20:08:56 2022

@author: czems
"""
import os
import cv2
from utils.api_config import CONFIG
from utils.tool import Point, Line, GetAngle, compute_IOself, posebox2yolobox, single_xyxy2xywh, filter_area
import logging
import logging.config

debuglogger = logging.getLogger(name='debuglog')
errorlogger = logging.getLogger(name='errorlog')
# from utils.general import xyxy2xywh

"""
label: [ 'worker', 'person', 'hat']
处理前的res:
        [{'label': 'worker',
          'conf': 0.7899675369262695,
          'box': [xmin, ymin, xmax, ymax],
          'class': 0},
         {'label': 'person',
          'conf': 0.6791136860847473,
          'box': [565, 491, 632, 631],
          'class': 1},
         ]
需要处理成的res:  左上宽高
[ { info: [ { rect: [ xmin, ymin, width, height ] } ], no_helmet_nums: 1 } ]
"""

class_config = CONFIG()
serial_id = 0  # 自增变量，需要测试效果

def hat_detect(det_bboxes):
    re_info = []
    helmet_msg = dict()
    return_msg = []
    persons_boxes = []
    hat_boxes = []
    for det_bbox in det_bboxes:
        x1, y1, x2, y2 = det_bbox["box"]
        if det_bbox["label"] == "person" or det_bbox["label"] == "worker":
            persons_boxes.append([x1, y1, x2, y2])
        else:
            hat_boxes.append([x1, y1, x2, y2])
    ass = {}
    # debuglogger.info(f'num of hats: {len(hat_boxes)}')
    # debuglogger.info(f'num of persons: {len(persons_boxes)}')
    for hat in hat_boxes:
        debuglogger.info(f'hat: {hat}')
        maxiou = 0
        maxheaght = -1  # iou最大的那个人的y1和安全帽中心点的距离
        maxiou_id = -1  # 最大iou的人的序号
        for i, person_box in enumerate(persons_boxes):
            # 计算iou阈值
            iou = compute_IOself([hat[0], hat[1], hat[2], hat[3]],
                                 [person_box[0], person_box[1], person_box[2], person_box[3]])
            idxheaght = (hat[1] + hat[3]) / 2 - person_box[1]  # 当前人的最高点与帽子的纵坐标中心点的差
            # debuglogger.info(f'person_box: {person_box}')
            # debuglogger.info(f'iou of person num_{i} is {iou}, idxheaght is {idxheaght}')
            # debuglogger.info(f'maxiou*0.8={maxiou*0.8}, maxheaght == -1 is {maxheaght == -1}, maxheaght != -1 is {maxheaght != -1}, 0 < idxheaght < maxheaght is {0 < idxheaght < maxheaght}')
            # debuglogger.info('-'*20)
            if maxiou < iou:
            # if maxiou * 0.8 < iou and (maxheaght == -1 or (maxheaght != -1 and 0 < idxheaght < maxheaght)):
                # 如果帽子和每个人的iou都是0，maxiou_id就不会被改变
                maxiou = iou
                maxiou_id = i  # 循环人的框给当前安全帽找到最合适的人的id
                maxheaght = idxheaght
                # debuglogger.info(f'current result - maxiou:{maxiou}, maxiou_id: {maxiou_id}, '
                #                  f'maxheaght: {maxheaght}, ass: {ass}')

        if maxiou_id in ass.keys():  # 如果一个人有两个可能的帽子
            if ass[maxiou_id] > maxiou > 0.1 and (
                    hat[1] < (persons_boxes[maxiou_id][1] +
                              (persons_boxes[maxiou_id][3] - persons_boxes[maxiou_id][1]) / 3)):  # 帽子在肩膀以上
                ass[maxiou_id] = maxiou
        else:
            if maxiou > 0.1 and (hat[1] < (persons_boxes[maxiou_id][1] +
                                           (persons_boxes[maxiou_id][3] - persons_boxes[maxiou_id][1]) / 3)):
                ass.update({maxiou_id: maxiou})

        # debuglogger.info(f'ass: {ass}')
        # debuglogger.info('-'*50)
    # ass: {2: 0.9867549668874173, 1: 0.9631578947368421}
    for i, bbox in enumerate(persons_boxes):
        if i in ass.keys():  # 带了安全帽
            # debuglogger.info(f'box {bbox} is with helmet')
            pass
        else:  # 没带安全帽, class_id 默认传4认为是没带安全帽，1,2,3表示不同颜色
            # debuglogger.info(f'box {bbox} didnot wear helmet')
            x1, y1, width, height = single_xyxy2xywh(bbox)
            re_info.append({'Class_ID': 4, 'rect': [x1, y1, width, height]})
    # print('re_info',re_info,type(re_info))
    if len(re_info) != 0:
        helmet_msg['info'] = re_info
        helmet_msg['no_helmet_nums'] = len(re_info)
        return_msg.append(helmet_msg)

    return return_msg


def uniform_detect(det_bboxes):
    re_info = []
    uniform_msg = dict()
    return_msg = []
    for det_bbox in det_bboxes:
        x1, y1, width, height = single_xyxy2xywh(det_bbox["box"])
        if det_bbox["label"] == "worker":
            re_info.append({'0_wear_result': 1, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
        elif det_bbox["label"] == "person":
            re_info.append({'0_wear_result': 0, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
    if len(re_info) != 0:
        uniform_msg['people_info'] = re_info
        return_msg.append(uniform_msg)
    return return_msg


def invade_detect(det_bboxes):
    re_info = []
    invade_msg = dict()
    return_msg = []
    channel_id = 1
    class_id = 0  # 类别，0为人，1为车
    global serial_id
    for det_bbox in det_bboxes:
        x1, y1, width, height = single_xyxy2xywh(det_bbox["box"])
        re_info.append({"0_serial_id": serial_id, '1_x': x1, '2_y': y1, '3_width': width, '4_height': height,
                        "5_channel": channel_id, "6_class_id": class_id})
        serial_id += 1
    if len(re_info) != 0:
        invade_msg['region_invade'] = re_info
        return_msg.append(invade_msg)
    return return_msg


def fire_detect(det_bboxes):
    re_info = []
    fire_msg = dict()
    return_msg = []
    for det_bbox in det_bboxes:
        x1, y1, width, height = single_xyxy2xywh(det_bbox["box"])
        if det_bbox["label"] == "fire":
            re_info.append({'0_type': 0, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
        elif det_bbox["label"] == "smoke":
            re_info.append({'0_type': 1, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
    if len(re_info) != 0:
        fire_msg['smoke_fire'] = re_info
        return_msg.append(fire_msg)
    return return_msg


def safetybelt_detect(det_bboxes):
    re_info = []
    return_msg = dict()
    offground_boxes = []
    safetybelt_boxes = []
    for det_bbox in det_bboxes:
        x1, y1, x2, y2 = det_bbox["box"]
        if det_bbox["label"] == "offground":
            offground_boxes.append([x1, y1, x2, y2])
        elif det_bbox["label"] == "safebelt":
            safetybelt_boxes.append([x1, y1, x2, y2])
    for offground in offground_boxes:
        iou_max = 0
        for safetybelt in safetybelt_boxes:
            iou = compute_IOself([offground[0], offground[1], offground[2], offground[3]],
                                 [safetybelt[0], safetybelt[1], safetybelt[2], safetybelt[3]])
            iou_max = max(iou, iou_max)
        if iou_max <= 0.4:
            re_info.append({'rect': offground})
    if len(re_info) != 0:
        return_msg['info'] = re_info
    return return_msg


def person_gesture_check(pose_keypoints, alarm_angle):
    angle = 360
    left_angle, right_angle = 360, 360
    no_face_points = sum(pose_keypoints[0:1, 0] >= 0) + sum(pose_keypoints[14:, 0] >= 0)
    no_keypoints = sum(pose_keypoints[:, 0] >= 0)
    side = 'no_smoke'

    if no_face_points < 4:
        return side
    if pose_keypoints[5][0] != -1 and pose_keypoints[6][0] != -1 and pose_keypoints[7][0] != -1:
        point5 = Point(pose_keypoints[5][0], pose_keypoints[5][1])
        point6 = Point(pose_keypoints[6][0], pose_keypoints[6][1])
        point7 = Point(pose_keypoints[7][0], pose_keypoints[7][1])
        line1 = Line(point6, point5)  # 肘和肩所在的直线
        line2 = Line(point6, point7)  # 肘和腕所在的子线
        left_angle = min(GetAngle(line1, line2), angle)

    if pose_keypoints[2][0] != -1 and pose_keypoints[3][0] != -1 and pose_keypoints[4][0] != -1:
        point2 = Point(pose_keypoints[2][0], pose_keypoints[2][1])
        point3 = Point(pose_keypoints[3][0], pose_keypoints[3][1])
        point4 = Point(pose_keypoints[4][0], pose_keypoints[4][1])
        line1 = Line(point3, point2)
        line2 = Line(point3, point4)
        right_angle = min(GetAngle(line1, line2), left_angle, angle)

    if ((0 < left_angle < alarm_angle) or (0 < right_angle < alarm_angle)) and no_keypoints > 9:
        debuglogger.info(f'left_angle: {left_angle}, right_angle: {right_angle}')
        if left_angle <= right_angle:
            side = 'left' + str(left_angle)
        else:
            side = 'right' + str(right_angle)

    return side


def person_smoke_detect(img, current_poses, det_bboxes, threshold, alarm_angle=180):
    re_info = []
    person_smoke_msg = dict()
    return_msg = []
    ori_height, ori_width, _ = img.shape
    # result_imgs = 'smoke_images/'
    # ori_images = 'smoke_images_ori/'
    # os.makedirs(result_imgs, exist_ok=True)
    # os.makedirs(ori_images, exist_ok=True)
    # global img_id
    # ori_img = img.copy()

    for group_id, pose in enumerate(current_poses):
        pose_keypoints = pose.keypoints
        side = person_gesture_check(pose_keypoints, alarm_angle)
        debuglogger.info(f'smoking side: {side}')

        if side != 'no_smoke':
            x1, y1, w, h = pose.bbox[:4]
            new_x1 = max(0, int(x1 - w * 0.1))
            new_y1 = max(0, int(y1 - h * 0.12))
            new_w = min(int(x1 + w + w * 0.1), ori_width) - new_x1
            new_h = min(int(y1 + h + h * 0.12), ori_height) - new_y1
            posebox = [new_x1, new_y1, new_x1 + new_w, new_y1 + new_h]
            corrected_box = posebox2yolobox(det_bboxes, posebox, threshold)
            x1, y1, x2, y2 = corrected_box
            re_info.append(
                {'0_smoke_result': 1, '1_left': x1, '2_top': y1, '3_width': x2-x1, '4_height': y2-y1})
            # re_info.append(
            #     {'0_smoke_result': 1, '1_left': new_x1, '2_top': new_y1, '3_width': new_w, '4_height': new_h})
            pose.draw(img)
            for i, point in enumerate(pose.keypoints):
                # print(i, point)
                # if point[0] != -1:
                if i in [2, 3, 4, 5, 6, 7] and point[0] != -1:
                    cv2.putText(img, str(i), (point[0], point[1]), cv2.FONT_HERSHEY_COMPLEX, 0.5, (255, 0, 0), 1)
            key_no = sum(pose_keypoints[:, 0] >= 0)
            cv2.putText(img, str(key_no), (100, 100), cv2.FONT_HERSHEY_COMPLEX, 2, (255, 0, 0), 2)
            # cv2.imwrite(result_imgs + str(img_id) + '.jpg', img)
            # cv2.imwrite(ori_images + str(img_id) + '.jpg', ori_img)
            # img_id += 1
    if len(re_info) != 0:
        person_smoke_msg['people_info'] = re_info
        return_msg.append(person_smoke_msg)

    return return_msg


def generel_detect(img, det_bboxes, interface, *threshold):
    debuglogger.info(f'det_bboxes before filter: {det_bboxes}, no of det_boxes: {len(det_bboxes)}')
    # 过滤类别
    det_bboxes = [i for i in det_bboxes if i['class'] in class_config.FILTER_IDS[interface]]
    debuglogger.info(f'det_bboxes after class filter: {det_bboxes}, no of det_boxes: {len(det_bboxes)}')
    # 过滤小面积的框
    pic_area = img.shape[0] * img.shape[1]
    minArea = pic_area * class_config.AREA_FILTER_PARAMS[interface]['filter_percent']
    filter_area_classid = class_config.AREA_FILTER_PARAMS[interface]['filter_id']
    det_bboxes = filter_area(det_bboxes, minArea, filter_area_classid)
    debuglogger.info(f'det_bboxes after area filter: {det_bboxes}, no of det_boxes: {len(det_bboxes)}')
    # 过滤阈值低的框, 抽烟阈值过滤，直接在主函数对安全帽里面阈值低的人进行过滤再传入posemodel
    if len(threshold) == 1:
        det_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[0]]
    else:
        smoke_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[0] if i['label'] == 'smoke']
        fire_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[1] if i['label'] == 'fire']
        det_bboxes = smoke_bboxes + fire_bboxes
    debuglogger.info(f'det_bboxes after confidence filter: {det_bboxes}, no of det_boxes: {len(det_bboxes)}')
    if interface == class_config.API_ID_HELMET:
        return_msg = hat_detect(det_bboxes)
    elif interface == class_config.API_ID_UNIFORM:
        return_msg = uniform_detect(det_bboxes)
    elif interface == class_config.API_ID_INVADE:
        return_msg = invade_detect(det_bboxes)
    elif interface == class_config.API_ID_SMOKE_FIRE:
        return_msg = fire_detect(det_bboxes)
    elif interface == class_config.API_ID_SAFEBELT:
        return_msg = safetybelt_detect(det_bboxes)
    return return_msg  # list
