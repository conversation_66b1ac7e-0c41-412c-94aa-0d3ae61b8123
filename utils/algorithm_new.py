#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Fri May  6 20:08:56 2022

@author: czems
"""

import cv2
import numpy as np
import math
import json
from utils.api_config import CONFIG

# from utils.general import xyxy2xywh

"""
label: [ 'worker', 'person', 'hat']
处理前的res:
        [{'label': 'worker',
          'conf': 0.7899675369262695,
          'box': [xmin, ymin, xmax, ymax],
          'class': 0},
         {'label': 'person',
          'conf': 0.6791136860847473,
          'box': [565, 491, 632, 631],
          'class': 1},
         ]
需要处理成的res:  左上宽高
[ { info: [ { rect: [ xmin, ymin, width, height ] } ], no_helmet_nums: 1 } ]
"""
res = eval(str([{'label': 'worker', 'conf': 0.7899675369262695, 'box': [714, 522, 762, 670], 'class': 0},
                {'label': 'person', 'conf': 0.6791136860847473, 'box': [565, 491, 632, 631], 'class': 1},
                {'label': 'hat', 'conf': 0.6791136860847473, 'box': [111, 415, 133, 440], 'class': 1},
                {'label': 'hat', 'conf': 0.6791136860847473, 'box': [111, 415, 133, 440], 'class': 2}
                ]))

res = eval(str([{'label': 'fire', 'conf': 0.7899675369262695, 'box': [714, 522, 762, 670], 'class': 0},
                {'label': 'smoke', 'conf': 0.6791136860847473, 'box': [565, 491, 632, 631], 'class': 1},
                {'label': 'fire', 'conf': 0.6791136860847473, 'box': [111, 415, 133, 440], 'class': 0},
                {'label': 'smoke', 'conf': 0.7791136860847473, 'box': [111, 415, 133, 440], 'class': 1}
                ]))

class_config = CONFIG()
serial_id = 0  # 自增变量，需要测试效果
# 暂时自己定义，后面试一下直接使用utils.metrics里面的bbox_iou函数
def compute_IOU(rec1, rec2):
    """
    计算两个矩形框的交并比。
    :param rec1: (x0,y0,x1,y1)      (x0,y0)代表矩形左上的顶点，（x1,y1）代表矩形右下的顶点。下同。
    :param rec2: (x0,y0,x1,y1)
    :return: 交并比IOU.
    """
    left_column_max = max(rec1[0], rec2[0])
    right_column_min = min(rec1[2], rec2[2])
    up_row_max = max(rec1[1], rec2[1])
    down_row_min = min(rec1[3], rec2[3])
    # 两矩形无相交区域的情况
    if left_column_max >= right_column_min or down_row_min <= up_row_max:
        return 0
    # 两矩形有相交区域的情况
    else:
        S1 = (rec1[2] - rec1[0]) * (rec1[3] - rec1[1])
        S2 = (rec2[2] - rec2[0]) * (rec2[3] - rec2[1])
        S_cross = (down_row_min - up_row_max) * (right_column_min - left_column_max)
        return S_cross / S1

def xyxy2xywh(box):
    x1, y1, x2, y2 = box
    w = x2 - x1
    h = y2 - y1
    return [x1, y1, w, h]

def hat_detect(det_bboxes):
    re_info = []
    helmet_msg = dict()
    return_msg = []
    persons_boxes = []
    hat_boxes = []
    for det_bbox in det_bboxes:
        x1, y1, x2, y2 = det_bbox["box"]
        if det_bbox["label"] == "person" or det_bbox["label"] == "worker":
            persons_boxes.append([x1, y1, x2, y2])
        else:
            hat_boxes.append([x1, y1, x2, y2])
    ass = {}
    for hat in hat_boxes:
        maxiou = 0
        maxheaght = -1
        maxiou_id = -1
        for i, person_box in enumerate(persons_boxes):
            # 计算iou阈值
            iou = compute_IOU([hat[0], hat[1], hat[2], hat[3]],
                              [person_box[0], person_box[1], person_box[2], person_box[3]])
            idxheaght = (hat[1] + hat[3]) / 2 - person_box[1]  # 当前人的最左点与帽子的横坐标中心点的差
            if maxiou * 0.8 < iou and (maxheaght == -1 or (maxheaght != -1 and 0 < idxheaght < maxheaght)):
                # 如果帽子和每个人的iou都是0，maxiou_id就不会被改变
                maxiou = iou
                maxiou_id = i  # 循环人的框给当前安全帽找到最合适的人的id
                maxheaght = idxheaght
        if maxiou_id in ass.keys():  # 如果一个人有两个可能的帽子
            if ass[maxiou_id] > maxiou > 0.1 and (
                    hat[1] < (persons_boxes[maxiou_id][1] +
                              (persons_boxes[maxiou_id][3] - persons_boxes[maxiou_id][1]) / 3)):  # 帽子在肩膀以上
                ass[maxiou_id] = maxiou
        else:
            if maxiou > 0.1 and (hat[1] < (persons_boxes[maxiou_id][1] +
                                           (persons_boxes[maxiou_id][3] - persons_boxes[maxiou_id][1]) / 3)):
                ass.update({maxiou_id: maxiou})
        # print("maxiou: ", maxiou)
    for i, bbox in enumerate(persons_boxes):
        if i in ass.keys():  # 带了安全帽
            pass
        else:  # 没带安全帽, class_id 默认传4认为是没带安全帽，1,2,3表示不同颜色
            x1, y1, width, height = xyxy2xywh(bbox)
            re_info.append({'Class_ID': 4, 'rect': [x1, y1, width, height]})
    # print('re_info',re_info,type(re_info))
    if len(re_info) != 0:
        helmet_msg['info'] = re_info
        helmet_msg['no_helmet_nums'] = len(re_info)
        return_msg.append(helmet_msg)
    # print(helmet_msg)
    return return_msg

def uniform_detect(det_bboxes):
    re_info = []
    uniform_msg = dict()
    return_msg = []
    for det_bbox in det_bboxes:
        x1, y1, width, height = xyxy2xywh(det_bbox["box"])
        if det_bbox["label"] == "worker":
            re_info.append({'0_wear_result': 1, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
        elif det_bbox["label"] == "person":
            re_info.append({'0_wear_result': 0, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
    if len(re_info) != 0:
        uniform_msg['people_info'] = re_info
        return_msg.append(uniform_msg)
    return return_msg

def invade_detect(det_bboxes):
    re_info = []
    invade_msg = dict()
    return_msg = []
    channel_id = 1
    class_id = 0  # 类别，0为人，1为车
    global serial_id
    for det_bbox in det_bboxes:
        x1, y1, width, height = xyxy2xywh(det_bbox["box"])
        re_info.append({"0_serial_id":serial_id, '1_x': x1, '2_y': y1, '3_width': width, '4_height': height,
                        "5_channel": channel_id, "6_class_id": class_id})
        serial_id += 1
    if len(re_info) != 0:
        invade_msg['region_invade'] = re_info
        return_msg.append(invade_msg)
    return return_msg

def fire_detect(det_bboxes):
    re_info = []
    fire_msg = dict()
    return_msg = []
    for det_bbox in det_bboxes:
        x1, y1, width, height = xyxy2xywh(det_bbox["box"])
        if det_bbox["label"] == "fire":
            re_info.append({'0_type': 0, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
        elif det_bbox["label"] == "smoke":
            re_info.append({'0_type': 1, '1_left': x1, '2_top': y1, '3_width': width, '4_height': height})
    if len(re_info) != 0:
        fire_msg['smoke_fire'] = re_info
        return_msg.append(fire_msg)
    return return_msg

def detect(det_bboxes, interface, *threshold):
    # 过滤类别
    det_bboxes = [i for i in det_bboxes if i['class'] in class_config.FILTER_IDS[interface]]
    # 过滤阈值低的框
    if len(threshold) == 1:
        det_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[0]]
    else:
        smoke_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[0] if i['label'] == 'smoke']
        fire_bboxes = [i for i in det_bboxes if i['conf'] >= threshold[1] if i['label'] == 'fire']
        det_bboxes = smoke_bboxes + fire_bboxes
    if interface == class_config.API_ID_HELMET:
        return_msg = hat_detect(det_bboxes)
    elif interface == class_config.API_ID_UNIFORM:
        return_msg = uniform_detect(det_bboxes)
    elif interface == class_config.API_ID_INVADE:
        return_msg = invade_detect(det_bboxes)
    elif interface == class_config.API_ID_SMOKE_FIRE:
        return_msg = fire_detect(det_bboxes)

    print('return_msg', return_msg, type(return_msg))
    return return_msg  # list


# info = [{'rect': ['xmin', 'ymin', 'width', 'height']}]
# x = [{'info': info, 'no_helmet_nums': 1}]
#
# print(res)
# detect(res, '30', 0.7, 0.7)
